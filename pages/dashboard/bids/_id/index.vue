<template>
  <div class="bid_details_wrapper">
    <div class="main_info">
      <div class="back_button">
        <nuxt-link :to="localePath(`/dashboard/${bid.byMe == true ? 'mybids' : 'bids'}`)">
          <svg class="icon">
            <use xlink:href="~/static/sprite.svg#angle-down"></use>
          </svg>
          {{ $t('admin.mybids_list') }}
        </nuxt-link>
      </div>
      <!-- end::back_button -->
      <div class="white_wrapper">
        <div class="line space_between">
          <div class="wrapper flex-wrap"
            >
            <h2 class="name">
              {{ bid.name }}
              <span class="serial"> #{{ parseFloat(bid.id) + 1000 }} </span>
            </h2>
            <span class="type">{{ bid.type }}</span>
            <span class="client-name" v-if="bid.byMe === true && bid.client && bid.client.admin_id === userData.id && bid.client.company_name">
              {{ bid.client.company_name }}
            </span>
          </div>
          <!-- end::wrapper -->
          <client-only>
            <div class="wrapper options" v-if="bid.byMe == true">
              <nuxt-link :to="localePath({ name: 'dashboard-mybids-id-edit' })" class="btn btn-default" v-if="
                bid.status == 1 &&
                bid.pending_deletion != 1 &&
                bid.permissions.can_edit
              ">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#edit"></use>
                </svg>
                {{ $t('admin.update_bid') }}
              </nuxt-link>
              <button type="button" class="btn btn-default danger-btn"
                v-if="bid.status == 1 && bid.pending_deletion != 1" @click="$bvModal.show('closeModal')">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#close_bid"></use>
                </svg>
                {{ $t('admin.close_bid') }}
              </button>
              <button type="button" class="btn btn-default" v-if="canDelete" @click="confirmDeleteBid">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#close_bid"></use>
                </svg>
                {{ $t('admin.delete_bid') }}
              </button>
              <button type="button" class="btn btn-default" v-if="canApproveDisapprove" @click="approveDeletion"
                :disabled="aprroveLoading">
                {{ $t('admin.approve_deletion') }}
              </button>
              <button type="button" class="btn btn-default danger-btn" v-if="canApproveDisapprove"
                @click="rejectDeletion" :disabled="rejectLoading">
                {{ $t('admin.reject_deletion') }}
              </button>
            </div>
            <!-- end::wrapper -->
            <div class="wrapper options_user" v-if="bid.byMe == false">
              <button type="button" class="btn btn-default btn-filled" v-if="
                bid.yourOffer == null && bid.offer_times.can_add_offer == true
              " @click="openModal('offer')">
                {{ $t('admin.apply_now') }}
              </button>
            </div>
            <!-- end::wrapper -->
          </client-only>
        </div>
        <!-- end::line -->
        <div class="line">
          <div class="wrapper">
            <div class="icon_wrapper">
              <svg class="icon">
                <use xlink:href="~/static/sprite.svg#location"></use>
              </svg>
            </div>
            <span class="value">{{ bid.region }}</span>
          </div>
          <!-- end::wrapper -->
          <div class="wrapper">
            <div class="icon_wrapper">
              <svg class="icon">
                <use xlink:href="~/static/sprite.svg#time"></use>
              </svg>
            </div>
            <span class="value">
              {{ $moment(bid.expiresAt).format('lll') }}
            </span>
            <span class="time_last" v-if="bid.status == 1 && timeRemaining">
              <span v-if="!timeRemaining.expired">
                {{ timeRemaining.days }} {{ $t('admin.days') }} -
                {{ timeRemaining.hours }} {{ $t('admin.hours') }} -
                {{ timeRemaining.minutes }} {{ $t('admin.min') }} -
                {{ timeRemaining.seconds }} {{ $t('admin.secds') }}
              </span>
              <span v-else class="expired">
                {{ $t('admin.expired') }}
              </span>
            </span>
            <span class="time_last" :class="{ active: bid.status == 1 }">
              {{ bid.detailed_status }}
            </span>
          </div>
          <!-- end::wrapper -->
        </div>
        <!-- end::line -->

        <div class="line">
          <div class="wrapper">
            <div class="icon_wrapper">
              <svg class="icon">
                <use xlink:href="~/static/sprite.svg#time"></use>
              </svg>
            </div>
            <span class="label">
              {{ $t('admin.bid_form.pricing_time') }}
            </span>
            <span class="value">
              {{ $moment(bid.offer_times.pricing_time_from).format('lll') }} -
              {{ $moment(bid.offer_times.pricing_time_to).format('lll') }}
            </span>
          </div>
          <!-- end::wrapper -->
        </div>
        <!-- end::line -->
        <div class="line flex-clear">
          <div class="wrapper">
            <div class="icon_wrapper">
              <svg class="icon">
                <use xlink:href="~/static/sprite.svg#attachment"></use>
              </svg>
            </div>
            <span class="value">
              {{ $t('admin.attachments') }}
              <!-- <span class="count">{{ bid.files.length }}</span> -->
            </span>
          </div>
          <!-- end::wrapper -->
          <div class="wrapper attachs">
            <!-- <a :href="file.file" v-for="(file, idx) in bid.files" :key="idx + 100" target="_blank">
              {{ file.name }}
            </a>
            <a :href="bid.media_url" target="_blank" v-if="bid.media_url != null">
              {{ $t('admin.external_url') }}
            </a> -->
            <a :href="bid.private_attachments_url" target="_blank" v-if="bid.private_attachments_url != null">
              {{ $t('admin.private_attachments') }}
            </a>
          </div>
          <!-- end::wrapper -->
        </div>
        <!-- end::line -->
        <div class="line flex-clear" v-if="bid.byMe == true">
          <div class="wrapper">
            <div class="icon_wrapper">
              <svg class="icon">
                <use xlink:href="~/static/sprite.svg#invite-user"></use>
              </svg>
            </div>
            <span class="value">
              {{ $t('admin.invitations') }}
              <span class="count">{{ bid.invidedSellers.length }}</span>
            </span>
          </div>
          <!-- end::wrapper -->
          <div class="wrapper bidders_wrapper">
            <div class="position-relative" v-for="(item, idx) in bid.invitedBiddersWithOffersCount" :key="idx">
              <img :src="item.image" alt="avatar" :class="{ registered: item.registered }" v-b-tooltip.hover
                :title="item.email" />
              <span class="subscription-mark" v-if="item.has_valid_subscription" v-b-tooltip.hover
                :title="$t('admin.has_valid_subscription')">
                ★
              </span>
            </div>
            <button type="button" class="btn" @click="triggerSuggestion" :title="$t('admin.invite_seller')"
              v-if="bid.status != 2">
              <!-- @click="$bvModal.show('invite')" -->
              <svg class="icon">
                <use xlink:href="~/static/sprite.svg#plus-transparent"></use>
              </svg>
            </button>
          </div>
          <!-- end::wrapper -->
        </div>
        <!-- end::line -->
      </div>
      <!-- end::white_wrapper -->
    </div>
    <!-- end::main_info -->

    <div class="row">
      <div class="col-lg-6">
        <div class="offers_wrapper">
          <div class="title_box d-flex justify-content-between align-items-center">
            <h4 class="title">{{ $t('admin.submitted_offers') }}</h4>
            <a :href="bid.report_url" class="mx-3 mb-1" style="font-weight: 500; color: #1e805d"
              v-if="bid.report_url && bid.permissions.can_generate_report" target="_blank">
              <span> {{ $t('admin.view_report') }} </span>
            </a>
          </div>

          <div class="offers_list" v-if="bid.byMe == false">
            <div class="line" :class="{ owned: item.client_id === userData.id }" v-for="(item, idx) in offers"
              :key="idx">
              <div class="item main_info_data">
                <span class="title">
                  {{ $t('admin.offer') + ' ' }} {{ idx + 1 }}
                </span>
                <svg class="icon" :class="{ arrow_up: lowestPrice === item.price }" v-if="lowestPrice === item.price">
                  <use xlink:href="~/static/sprite.svg#boxed-arrow-up"></use>
                </svg>
                <svg class="icon" v-else>
                  <use xlink:href="~/static/sprite.svg#boxed-arrow-down"></use>
                </svg>
                <span class="offer_label" v-if="item.client_id === userData.id">
                  {{ $t('admin.your_offer') }}
                </span>
              </div>
              <!-- end:: item -->
              <div class="item price_wrapper">
                <div class="price" v-if="bid.byMe == false && bid.showPrices == true">
                  {{ productPrice(item.price) }}
                  {{ ' ' + $t('admin.currancy') }}
                </div>
                <button type="button" class="btn btn-default btn-transparent" @click="openModal('offer')"
                  v-if="item.client_id == userData.id && bid.status != 2">
                  {{ $t('admin.edit_offer') }}
                </button>
              </div>
              <!-- end:: item -->
              <div class="item options" v-if="bid.byMe">
                <a href="javascript:;" @click="fetchLog(bid.id, item.id)">
                  <span> {{ $t('admin.view_offers_log') }} </span>
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#arrow-stoke"></use>
                  </svg>
                </a>
              </div>
            </div>
            <!-- end::line -->
          </div>

          <div class="offers_list" v-if="bid.byMe == true">
            <div class="line" :class="{
              owned: item.client_id == userData.id,
              flex_clear: bid.byMe == true || item.client_id == userData.id,
            }" v-for="(item, idx) in offers" :key="idx">
              <div class="item main_info_data">
                <div>
                  <span class="title">
                    <span v-if="bid.byMe == false">
                      {{ $t('admin.offer') + ' ' }} {{ idx + 1 }}
                    </span>
                    <span v-if="bid.byMe == true && bid.status == 2" class="hoverable" v-b-toggle.client_info
                      @click="getUserData(item)">
                      {{ item.name }}
                    </span>
                    <span v-if="bid.byMe == true && bid.status != 2">
                      {{ $t('admin.offer') + ' ' }} {{ idx + 1 }}
                    </span>
                  </span>
                  <svg class="icon" :class="{ arrow_up: lowestPrice == item.price }" v-if="lowestPrice == item.price">
                    <use xlink:href="~/static/sprite.svg#boxed-arrow-up"></use>
                  </svg>
                  <svg class="icon" v-else>
                    <use xlink:href="~/static/sprite.svg#boxed-arrow-down"></use>
                  </svg>
                  <span class="offer_label" v-if="item.client_id == userData.id">
                    {{ $t('admin.your_offer') }}
                  </span>
                </div>
                <div class="info_wrapper">
                  <div class="row">
                    <div class="col-lg-12">
                      <div class="info" v-if="bid.byMe === true && bid.status === 2">
                        <div class="icon_wrapper">
                          <svg class="icon">
                            <use xlink:href="~/static/sprite.svg#envelope"></use>
                          </svg>
                        </div>
                        <span> {{ item.client.email }} </span>
                      </div>
                      <!-- end::info -->
                    </div>
                    <!-- end::col -->
                    <div class="col-lg-12" v-if="bid.byMe === true && bid.status === 2">
                      <div class="info">
                        <div class="icon_wrapper">
                          <svg class="icon">
                            <use xlink:href="~/static/sprite.svg#phone"></use>
                          </svg>
                        </div>
                        <span> {{ item.client.phone }}</span>
                      </div>
                      <!-- end::info -->
                    </div>
                    <!-- end::col -->
                    <div class="col-lg-12" v-if="bid.byMe === true && bid.status === 2">
                      <div class="info">
                        <div class="d-flex align-items-center">
                          <div class="icon_wrapper">
                            <svg class="icon">
                              <use xlink:href="~/static/sprite.svg#attachment"></use>
                            </svg>
                          </div>
                          <span class="label">
                            {{ $t('admin.attachments') }}
                            <span class="count">{{ item.files.length }}</span>
                          </span>
                        </div>
                        <div class="attachs">
                          <a :href="file.file" v-for="(file, idx) in item.files" :key="idx + 100" target="_blank">
                            {{ file.name }}
                          </a>
                        </div>
                        <!-- end::wrapper -->
                      </div>
                      <!-- end::info -->
                    </div>
                  </div>
                  <!-- end::row -->
                </div>
              </div>
              <!-- end:: item -->
              <div class="item price_wrapper">
                <div class="price" v-if="bid.byMe == true && bid.status == 2">
                  {{ productPrice(item.price) }}
                  {{ ' ' + $t('admin.currancy') }}
                </div>
              </div>
              <!-- end:: item -->
              <div class="item justify-content-between flex-wrap options" v-if="bid.byMe && bid.status == 2">
                <a href="javascript:;" class="mx-3 mb-1" @click="handleGenerateReport(item)" v-if="!bid.report_url">
                  <span> {{ $t('admin.generate_report') }} </span>
                </a>
                <a href="javascript:;" class="mx-3 mb-1" @click="fetchLog(bid.id, item.id)">
                  <span> {{ $t('admin.view_offers_log') }} </span>
                </a>
              </div>
            </div>
            <!-- end::line -->
          </div>
          <!-- end::offers_list -->
        </div>
        <!-- end::offers_wrapper  -->
      </div>
      <!-- end::col -->

      <div class="col-lg-6">
        <div class="comments_wrapper">
          <div class="title_box">
            <h4 class="title">{{ $t('admin.comments') }}</h4>
          </div>
          <!-- end::title_box -->

          <div class="main_comment_form">
            <client-only>
              <ValidationObserver ref="maincomment">
                <b-form @submit.prevent="handleCommentForm">
                  <ValidationProvider rules="required" slim v-slot="{ errors }">
                    <b-form-group class="send_wrappeer">
                      <b-form-textarea type="text" v-model="comment_form.comment" :class="{ invalid: errors[0] }"
                        :placeholder="$t('admin.add_comment')" @input="autoExpand('maincomment')" rows="1"
                        id="maincomment"></b-form-textarea>
                      <div class="d-flex justify-content-end mt-1">
                        <button type="submit" class="btn btn-default"
                          :disabled="errors[0] || comment_form.comment == null">
                          <svg class="icon">
                            <use xlink:href="~/static/sprite.svg#send"></use>
                          </svg>
                        </button>
                      </div>
                    </b-form-group>
                  </ValidationProvider>
                </b-form>
              </ValidationObserver>
            </client-only>
          </div>
          <!-- end::main_comment_form -->

          <div class="comments_list">
            <div class="line" v-for="(item, index) in comments" :key="index">
              <h5 class="title">
                {{ getCommentDisplayName(item) }}
              </h5>
              <p class="message">
                {{ item.comment }}
              </p>
              <a href="javascript:;" class="show_subcomments" @click="showSubComments(item)">
                {{ $t('admin.show_more_comment', { value: item.replys.length }) }}
              </a>
              <!--                {{ `View ${item.replys.length} more comments` }}-->
              <client-only>
                <ValidationObserver ref="subcomment" v-if="subcomment_form.length > 0">
                  <b-form @submit.prevent="handleSubCommentForm(index)">
                    <ValidationProvider>
                      <b-form-group class="send_wrappeer">
                        <b-form-textarea type="text" v-model="subcomment_form[index].comment"
                          :placeholder="$t('admin.reply_to_comment')" @input="autoExpand('subcomment')"
                          id="subcomment"></b-form-textarea>
                        <div class="d-flex justify-content-end mt-1">
                          <button type="submit" class="btn btn-default" :disabled="subcomment_form[index].comment == null ||
                            subcomment_form[index].comment == ''
                            ">
                            <svg class="icon">
                              <use xlink:href="~/static/sprite.svg#send"></use>
                            </svg>
                          </button>
                        </div>
                      </b-form-group>
                    </ValidationProvider>
                  </b-form>
                </ValidationObserver>
              </client-only>
            </div>
            <!-- end::line -->
          </div>
        </div>
        <!-- end::offers_wrapper  -->
      </div>
      <!-- end::col -->
    </div>
    <!-- end:: row -->

    <!-- start:: modals -->
    <ApplyOffer :lowest="bid.lowestprice" :item="selected_offer" @hide-modal="hideModal" :show_prices="bid.showPrices">
    </ApplyOffer>

    <InviteModal @hide-modal="hideModal"></InviteModal>

    <CloseBid :dropdown="closes"></CloseBid>

    <SubComments :item="sucomments_list" :bid="bid" @hide-modal="hideModal"></SubComments>
    <!-- end:: modals -->

    <!-- start:: client_info -->
    <b-sidebar id="client_info" :title="$t('admin.bidder_profile')" shadow v-model="user_sidebar_status">
      <div class="px-3 py-3" v-if="selected_user_offer">
        <div class="basic_info">
          <div class="avatar_wrapper">
            <img :src="selected_user_offer.client.logo" alt="avatar" />
            <h4>{{ selected_user_offer.name }}</h4>
          </div>
          <!-- end::avatar_wrapper -->
          <div class="personal_info">
            <h5 class="title">{{ $t('admin.personal_info') }}</h5>
            <div class="wrapper">
              <p>{{ $t('admin.register.phone') }}</p>
              <p>{{ selected_user_offer.client.phone }}</p>
            </div>
            <!-- end::wrapper -->
            <div class="wrapper mb-0">
              <p>{{ $t('admin.register.email') }}</p>
              <p>{{ selected_user_offer.client.email }}</p>
            </div>
            <!-- end::wrapper -->
          </div>
          <!-- end::personal_info -->
        </div>
        <!-- end::basic_info -->

        <div class="company_info">
          <h5 class="title">{{ $t('admin.personal_info') }}</h5>
          <div class="wrapper">
            <p>{{ $t('admin.register.company_name') }}</p>
            <p>{{ selected_user_offer.client.company_name }}</p>
          </div>
          <!-- end::wrapper -->
          <div class="wrapper">
            <p>{{ $t('admin.location') }}</p>
            <p>{{ selected_user_offer.client.region }}</p>
          </div>
          <!-- end::wrapper -->
          <div class="wrapper">
            <p>{{ $t('admin.register.company_website') }}</p>
            <p>{{ `www.munaqes.com` }}</p>
          </div>
          <!-- end::wrapper -->
          <div class="wrapper">
            <p>{{ $t('admin.register.company_profile') }}</p>
            <a :href="selected_user_offer.client.cv" target="_blank">
              {{ selected_user_offer.client.cv.split('/').pop().toLowerCase() }}
            </a>
          </div>
          <!-- end::wrapper -->
          <div class="wrapper mb-0">
            <p>{{ $t('admin.register.cr_number') }}</p>
            <p>{{ selected_user_offer.client.cr_number }}</p>
          </div>
          <!-- end::wrapper -->
        </div>
        <!-- end::company_info -->
      </div>
    </b-sidebar>
    <!-- end:: client_info -->

    <!-- start:: client_info -->
    <b-sidebar id="offers_log" :title="$t('admin.offer_log')" shadow v-model="offer_sidebar">
      <div class="px-3 py-3" v-if="offer_logs.length > 0">
        <div class="offer_card_wrapper" :class="{ current: offer_logs.length - 1 == idx }"
          v-for="(item, idx) in offer_logs" :key="item.id">
          <div class="line">
            <span class="title" v-if="offer_logs.length - 1 == idx">
              {{ $t('admin.current_offer') }}
            </span>
            <span class="title" v-else>
              {{ $t('admin.offer') + ' ' + (idx + 1) }}
            </span>
            <span class="date">
              {{ $moment(item.created_at).format('Do MMMM YYYY, hh:mm a') }}
            </span>
          </div>
          <!-- end::line -->

          <div class="line" v-if="bid.byMe == true && bid.status == 2">
            <span class="price">
              {{ productPrice(item.price) + ' ' + $t('admin.currancy') }}
            </span>
          </div>
          <!-- end::line -->

          <div class="line" v-if="offer_logs.length - 1 == idx">
            <span class="ratio">
              <span class="percent">
                {{ item.increaseRatio + ' %' }}
                <svg class="icon" :class="{ plus: item.increaseRatio < 0 }">
                  <use xlink:href="~/static/sprite.svg#ratio-arrow"></use>
                </svg>
              </span>
              <span class="text">
                <span v-if="item.increaseRatio > 0">{{
                  $t('admin.increase')
                  }}</span>
                <span v-else> {{ $t('admin.decrease') }}</span>
                {{ ' ' + $t('admin.ratio_hint') }}
              </span>
            </span>
          </div>
          <!-- end::line -->
        </div>
      </div>
    </b-sidebar>
    <!-- end:: client_info -->

    <b-sidebar v-model="invitations_sidebar" :title="$t('admin.list_available_invitations')" shadow backdrop
      width="450px">
      <template #footer>
        <button type="button" class="btn btn-default d-block w-100" style="border-radius: 0px"
          @click="handleInviteBidderReq" :disabled="disabled_submit_invite">
          {{ $t('admin.invite_seller') }}
        </button>
      </template>

      <b-overlay :show="disabled" rounded="sm">
        <div class="px-3 py-3">
          <b-form-group>
            <b-input-group>
              <b-form-input type="search" :placeholder="$t('admin.searchOrAddEmail')" v-model="searchText"
                @input="handleInput"></b-form-input>
            </b-input-group>
          </b-form-group>
          <div class="body_wrapper">
            <ul class="suggestions" v-if="searchText != '' && filteredTagsWithSearch.length">
              <li v-for="(tag, index) in filteredTagsWithSearch" :key="index" @click="addTag(tag)" :class="{
                selected: selectedTags.includes(tag.email),
                'new-email': tag.isNewEmail,
              }" class="d-flex align-items-center">
                <img :src="tag.logo
                    ? tag.logo
                    : 'https://backend-api.munaqes.com/assets/media/users/default.jpg'
                  " :alt="tag.company_name ? tag.company_name : tag.email" class="logo-img" />
                <div class="info-wrapper">
                  <span class="company-name" v-if="!tag.isNewEmail">
                    {{ tag.company_name ? tag.company_name : '' }}
                  </span>
                  <span class="company-name" v-if="tag.isNewEmail" style="font-style: italic; color: #666">
                    {{ $t('admin.invite_new_email') || 'Invite new email' }}
                  </span>
                  <span class="email">{{ tag.email }}</span>
                </div>
              </li>
            </ul>
            <ul class="suggestions" v-if="searchText == '' && filteredTags.length">
              <li v-for="(tag, index) in filteredTags" :key="index" @click="addTag(tag)"
                :class="{ selected: selectedTags.includes(tag.email) }" class="d-flex align-items-center">
                <img :src="tag.logo
                    ? tag.logo
                    : 'https://backend-api.munaqes.com/assets/media/users/default.jpg'
                  " :alt="tag.company_name ? tag.company_name : tag.email" class="logo-img" />
                <div class="info-wrapper">
                  <span class="company-name">
                    {{ tag.company_name ? tag.company_name : '' }}
                  </span>
                  <span class="email">{{ tag.email }}</span>
                </div>
              </li>
            </ul>
          </div>
          <!-- end::body_wrapper -->
        </div>
      </b-overlay>
    </b-sidebar>

    <GenerateReport :selected_offer="selected_offer" @close-generate-report="handleLockGenerateReport"></GenerateReport>
  </div>
</template>

<script src="~/pages/dashboard/bids/_id/-script.js"></script>

<style lang="scss">
@import '~/pages/dashboard/bids/_id/-style.scss';
</style>

<style lang="scss" scoped>
.logo-img {
  width: 50px;
  height: 50px;
  border-radius: 100%;
  border: 1px solid #eee;
}

.body_wrapper {
  max-height: 300px;
}

.tags-input {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5em;
  width: 100%;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5em;
}

.info-wrapper {
  margin-inline-start: 10px;
  width: calc(100% - 60px);
}

.company-name,
.email {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.tag {
  background: #dedede;
  border-radius: 5px;
  padding: 0.2em 0.5em;
  display: flex;
  align-items: center;
  background: #1e805d29;
  color: #1e805d;
  font-weight: 500;
  font-size: 14px;

  .close {
    margin-inline-start: 0.25em;
    text-shadow: none;
    font-weight: 500;
    font-size: 1.25rem;
    cursor: pointer;
  }
}

.suggestions {
  list-style: none;
  margin-top: 0.5em;
  padding: 0;
}

.suggestions li {
  cursor: pointer;
  padding: 0.5em;
  margin-bottom: 0.25rem;
  font-weight: 400;
  border-radius: 5px;

  &.selected {
    background: #1e805d29;
    color: #1e805d;
  }
}

.suggestions li:hover {
  background: #f0f0f0;
}

.suggestions li.new-email {
  border: 1px dashed #1e805d;
  background: #f8fffe;
}

.suggestions li.new-email:hover {
  background: #f0f9f6;
}

.form-control {
  height: 48px;
  border: 1px solid #d0d5dd;
  box-shadow: none;
  border-radius: 8px !important;

  &::placeholder {
    font-size: 14px;
  }

  &.invalid {
    border-color: #cc0000;

    &::placeholder {
      color: #cc0000;
    }

    &:focus {
      border-color: #cc0000;
    }
  }

  &:focus {
    border-color: $base-color;
  }
}

.btn-default {
  .icon {
    width: 20px;
    height: 20px;
  }

  background-color: $base-color;
  color: #fff;
  border-radius: 8px;
}

.time_last {
  .expired {
    color: #dc3545;
    font-weight: bold;
  }
}
</style>
